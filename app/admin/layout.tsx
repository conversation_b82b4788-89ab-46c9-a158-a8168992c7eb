"use client";

import { usePathname } from "next/navigation";
import { NotificationCenter } from "@/components/admin/notification-center";
import { AdminSidebar } from "@/components/admin/admin-sidebar";
import { AdminBreadcrumb } from "@/components/admin/admin-breadcrumb";
import { EnhancedAdminHeader } from "@/components/admin/enhanced-admin-header";
import { AdminAuthProvider } from "@/components/admin/admin-auth-provider";
import { AdminLayoutProvider } from "@/providers/admin-layout-provider";
import { EditorLayout } from "@/components/admin/editor-layout";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { Separator } from "@/components/ui/separator";
import { useSidebar } from "@/components/ui/sidebar";
import { useEffect } from "react";

function AdminLayout_Default({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const { state, open, setOpen } = useSidebar();

  // Check if we're on the login page
  const isLoginPage = pathname === "/admin/login";

  // Check if we're on editor pages
  //LAYOUT EDITOR - Designs the Layout in which PAGES are rendered within
  const isLayoutBuilderRoute = pathname?.match(
    /^\/admin\/system\/layouts\/edit\/[^\/]+$/
  );
  //PAGES EDITOR - Designs the Page in which BLOCKS are rendered within
  const isPageBuilderRoute = pathname?.match(
    /^\/admin\/system\/pages\/[^\/]+\/edit$/
  );
  //BLOCK EDITOR - Designs the COMPONENTS that can be used within PAGES/LAYOUTS
  const isBlockBuilderRoute = pathname?.match(
    /^\/admin\/system\/blocks\/[^\/]+\/edit$/
  );
  //TEMPLATE EDITOR - Designs the Templates Which defines How POSTS are rendered Dynamically using PAGE, LAYOUTS Or BLOCKS
  const isTemplateBuilderRoute = pathname?.match(
    /^\/admin\/system\/templates\/[^\/]+\/edit$/
  );

  const isEditorPage =
    isBlockBuilderRoute ||
    isPageBuilderRoute ||
    isLayoutBuilderRoute ||
    isTemplateBuilderRoute;

  // For login page, render with minimal wrappers
  if (isLoginPage) {
    return <AdminAuthProvider>{children}</AdminAuthProvider>;
  }

  useEffect(() => {
    if (!isEditorPage && !open) {
      setOpen(true);
    }
  }, [isEditorPage])

  return (
    <AdminAuthProvider>
      <AdminLayoutProvider>
        <AdminSidebar collapsible={"icon"} />

        {isEditorPage ? (
          // Editor Layout - Sidebar collapsed, full editor interface
          <>{children}</>
        ) : (
          // Normal Admin Layout
          <main className="flex flex-col flex-1 h-screen overflow-hidden">
            <EnhancedAdminHeader />
            <div className="flex-1 overflow-auto">
              <div className="container py-6">{children}</div>
            </div>
          </main>
        )}
      </AdminLayoutProvider>
    </AdminAuthProvider>
  );
}

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <SidebarProvider>
      <AdminLayout_Default>{children}</AdminLayout_Default>
    </SidebarProvider>
  );
}
