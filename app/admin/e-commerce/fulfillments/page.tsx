'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { Package, Truck, MapPin, Clock, CheckCircle, XCircle, Search, Filter, Plus, Eye, Edit, Trash2, Home } from 'lucide-react'
import { toast } from 'sonner'
import { useFulfillments } from '@/lib/ecommerce/hooks/use-fulfillments'
import { EnhancedFulfillmentList } from '@/components/admin/fulfillments/enhanced-fulfillment-list'
import { FulfillmentDashboard } from '@/components/admin/fulfillment/fulfillment-dashboard'


export default function FulfillmentsPage() {
  const router = useRouter()
  const { fulfillments, metrics, loading, error, fetchFulfillments, fetchMetrics, createFulfillment, updateFulfillmentStatus } = useFulfillments()
  const [selectedFulfillment, setSelectedFulfillment] = useState<any>(null)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  useEffect(() => {
    fetchFulfillments()
    fetchMetrics()
  }, [fetchFulfillments, fetchMetrics])

  const handleCreateFulfillment = () => {
    setShowCreateDialog(true)
  }

  const handleEditFulfillment = (fulfillment: any) => {
    setSelectedFulfillment(fulfillment)
    // Navigate to edit page or open edit dialog
    router.push(`/admin/fulfillments/${fulfillment.id}/edit`)
  }

  const handleViewFulfillment = (fulfillment: any) => {
    router.push(`/admin/fulfillments/${fulfillment.id}`)
  }

  const handleTrackShipment = (fulfillment: any) => {
    if (fulfillment.trackingUrl) {
      window.open(fulfillment.trackingUrl, '_blank')
    } else {
      toast.info('No tracking URL available for this shipment')
    }
  }

  const handleFiltersChange = (filters?: any) => {
    fetchFulfillments(filters)
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">
              <Home className="h-4 w-4" />
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Fulfillments</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Fulfillments</h1>
          <p className="text-muted-foreground">
            Manage order fulfillments, shipping, and tracking
          </p>
        </div>
        <Button onClick={handleCreateFulfillment}>
          <Plus className="mr-2 h-4 w-4" />
          Create Fulfillment
        </Button>
      </div>

      {/* Tabs for Dashboard and Management */}
      <Tabs defaultValue="dashboard" className="space-y-6">
        <TabsList>
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="management">Management</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard">
          <FulfillmentDashboard />
        </TabsContent>

        <TabsContent value="management" className="space-y-6">
          <EnhancedFulfillmentList
            onCreateFulfillment={handleCreateFulfillment}
            onEditFulfillment={handleEditFulfillment}
            onViewFulfillment={handleViewFulfillment}
            onTrackShipment={handleTrackShipment}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
