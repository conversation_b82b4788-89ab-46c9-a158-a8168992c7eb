'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { ArrowLeft, Package, Save, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { toast } from 'sonner'
import type { Product } from '@/lib/ecommerce/types'

// Form validation schema for general tab
const generalFormSchema = z.object({
  title: z.string().min(1, 'Product title is required').max(255, 'Title is too long'),
  description: z.string().min(1, 'Product description is required').max(5000, 'Description is too long'),
  slug: z.string().optional(),
  vendor: z.string().optional(),
  productType: z.string().optional(),
  status: z.enum(['active', 'draft', 'archived']),
  weight: z.number().min(0).optional().nullable(),
  weightUnit: z.string().optional(),
  requiresShipping: z.boolean(),
  isTaxable: z.boolean(),
})

type GeneralFormData = z.infer<typeof generalFormSchema>

export default function ProductGeneralEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<GeneralFormData>({
    resolver: zodResolver(generalFormSchema),
    mode: 'onChange',
    defaultValues: {
      title: '',
      description: '',
      slug: '',
      vendor: '',
      productType: '',
      status: 'draft',
      weight: null,
      weightUnit: 'kg',
      requiresShipping: true,
      isTaxable: true,
    },
  })

  const watchedTitle = watch('title')

  // Update form when product loads
  useEffect(() => {
    if (product) {
      setValue('title', product.title || '')
      setValue('description', product.description || '')
      setValue('slug', product.slug || '')
      setValue('vendor', product.vendor || '')
      setValue('productType', product.productType || '')
      setValue('status', product.status || 'draft')
      setValue('weight', product.weight || null)
      setValue('weightUnit', product.weightUnit || 'kg')
      setValue('requiresShipping', product.requiresShipping ?? true)
      setValue('isTaxable', product.isTaxable ?? true)
    }
  }, [product, setValue])

  // Auto-generate slug from title
  useEffect(() => {
    if (watchedTitle && !product?.slug) {
      const slug = watchedTitle
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/^-+|-+$/g, '')
      setValue('slug', slug)
    }
  }, [watchedTitle, setValue, product?.slug])

  const onSubmit = async (data: GeneralFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        ...data,
        weight: data.weight || undefined,
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('General information updated successfully')
        router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update product')
      console.error('Error updating product:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">General Information</h1>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <Button
          type="submit"
          disabled={isUpdating || isSubmitting || !isDirty}
          className="min-w-[140px]"
        >
          {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Package className="mr-2 h-5 w-5" />
            Basic Information
          </CardTitle>
          <CardDescription>
            Essential product details and description
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Product Title *</Label>
              <Input
                id="title"
                {...register('title')}
                placeholder="Enter product title"
              />
              {errors.title && (
                <p className="text-sm text-red-500">{errors.title.message}</p>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="slug">URL Slug</Label>
              <Input
                id="slug"
                {...register('slug')}
                placeholder="product-url-slug"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description *</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Describe your product..."
              rows={4}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="vendor">Vendor</Label>
              <Input
                id="vendor"
                {...register('vendor')}
                placeholder="Brand or vendor name"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="productType">Product Type</Label>
              <Input
                id="productType"
                {...register('productType')}
                placeholder="e.g., T-Shirt, Dress, Shoes"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="status">Status</Label>
              <Select
                value={watch('status')}
                onValueChange={(value: 'active' | 'draft' | 'archived') =>
                  setValue('status', value)
                }
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="weightUnit">Weight Unit</Label>
              <Select
                value={watch('weightUnit')}
                onValueChange={(value) => setValue('weightUnit', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="kg">Kilograms (kg)</SelectItem>
                  <SelectItem value="g">Grams (g)</SelectItem>
                  <SelectItem value="lb">Pounds (lb)</SelectItem>
                  <SelectItem value="oz">Ounces (oz)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="weight">Weight</Label>
              <Input
                id="weight"
                type="number"
                step="0.01"
                {...register('weight', { valueAsNumber: true })}
                placeholder="0.00"
              />
            </div>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="requiresShipping"
                  checked={watch('requiresShipping')}
                  onCheckedChange={(checked) => setValue('requiresShipping', checked)}
                />
                <Label htmlFor="requiresShipping">Requires shipping</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="isTaxable"
                  checked={watch('isTaxable')}
                  onCheckedChange={(checked) => setValue('isTaxable', checked)}
                />
                <Label htmlFor="isTaxable">Taxable</Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
