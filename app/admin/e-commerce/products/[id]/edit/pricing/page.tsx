'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { ArrowLeft, DollarSign, Save, Loader2 } from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { ZarPriceInput } from '@/components/admin/zar-price-input'
import { toast } from 'sonner'
import type { Product } from '@/lib/ecommerce/types'

// Form validation schema for pricing tab
const pricingFormSchema = z.object({
  price: z.number().min(0.01, 'Price must be greater than 0'),
  compareAtPrice: z.number().min(0).optional().nullable(),
  costPerItem: z.number().min(0).optional().nullable(),
})

type PricingFormData = z.infer<typeof pricingFormSchema>

export default function ProductPricingEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<PricingFormData>({
    resolver: zodResolver(pricingFormSchema),
    mode: 'onChange',
    defaultValues: {
      price: 0,
      compareAtPrice: null,
      costPerItem: null,
    },
  })

  // Update form when product loads
  useEffect(() => {
    if (product) {
      setValue('price', product.price?.amount || 0)
      setValue('compareAtPrice', product.compareAtPrice?.amount || null)
      setValue('costPerItem', product.costPerItem?.amount || null)
    }
  }, [product, setValue])

  const onSubmit = async (data: PricingFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        price: data.price,
        compareAtPrice: data.compareAtPrice && data.compareAtPrice > 0 ? data.compareAtPrice : undefined,
        costPerItem: data.costPerItem && data.costPerItem > 0 ? data.costPerItem : undefined,
        currency: 'ZAR',
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('Pricing updated successfully')
        router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update pricing')
      console.error('Error updating product pricing:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Pricing</h1>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <Button
          type="submit"
          disabled={isUpdating || isSubmitting || !isDirty}
          className="min-w-[140px]"
        >
          {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          <Save className="mr-2 h-4 w-4" />
          Save Changes
        </Button>
      </div>

      {/* Form Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <DollarSign className="mr-2 h-5 w-5" />
            Pricing
          </CardTitle>
          <CardDescription>
            Set your product prices in South African Rand (ZAR)
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ZarPriceInput
              label="Regular Price *"
              value={watch('price')}
              onChange={(value) => setValue('price', value, { shouldValidate: true })}
              required
              description="The standard selling price"
              error={errors.price?.message}
            />

            <ZarPriceInput
              label="Compare at Price"
              value={watch('compareAtPrice') || 0}
              onChange={(value) => setValue('compareAtPrice', value > 0 ? value : null, { shouldValidate: true })}
              description="Optional original price for comparison (shows as strikethrough)"
              error={errors.compareAtPrice?.message}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ZarPriceInput
              label="Cost per Item"
              value={watch('costPerItem') || 0}
              onChange={(value) => setValue('costPerItem', value > 0 ? value : null, { shouldValidate: true })}
              description="Your cost for this item (for profit calculations)"
              error={errors.costPerItem?.message}
            />

            {/* Profit Margin Display */}
            {watch('price') > 0 && watch('costPerItem') && watch('costPerItem')! > 0 && (
              <div className="space-y-2">
                <Label>Profit Margin</Label>
                <div className="p-3 bg-muted rounded-md">
                  <div className="text-sm space-y-1">
                    <div className="flex justify-between">
                      <span>Selling Price:</span>
                      <span>R {watch('price').toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Cost:</span>
                      <span>R {watch('costPerItem')!.toFixed(2)}</span>
                    </div>
                    <Separator />
                    <div className="flex justify-between font-medium">
                      <span>Profit:</span>
                      <span className={watch('price') > watch('costPerItem')! ? 'text-green-600' : 'text-red-600'}>
                        R {(watch('price') - watch('costPerItem')!).toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm text-muted-foreground">
                      <span>Margin:</span>
                      <span>
                        {(((watch('price') - watch('costPerItem')!) / watch('price')) * 100).toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </form>
  )
}
