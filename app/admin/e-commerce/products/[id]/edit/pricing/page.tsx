'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  ArrowLeft,
  DollarSign,
  Save,
  Loader2,
  TrendingUp,
  TrendingDown,
  Calculator,
  Info,
  AlertTriangle,
  CheckCircle,
  Eye
} from 'lucide-react'
import Link from 'next/link'
import { useProduct, useProductMutations } from '@/lib/ecommerce/hooks/use-products'
import { ZarPriceInput } from '@/components/admin/zar-price-input'
import { toast } from 'sonner'
import { cn } from '@/lib/utils'
import type { Product } from '@/lib/ecommerce/types'

// Form validation schema for pricing tab
const pricingFormSchema = z.object({
  price: z.number()
    .min(0.01, 'Price must be greater than R0.01')
    .max(999999.99, 'Price cannot exceed R999,999.99'),
  compareAtPrice: z.number()
    .min(0, 'Compare price cannot be negative')
    .max(999999.99, 'Compare price cannot exceed R999,999.99')
    .optional()
    .nullable(),
  costPerItem: z.number()
    .min(0, 'Cost cannot be negative')
    .max(999999.99, 'Cost cannot exceed R999,999.99')
    .optional()
    .nullable(),
}).refine(
  (data) => {
    if (data.compareAtPrice && data.compareAtPrice > 0) {
      return data.compareAtPrice > data.price
    }
    return true
  },
  {
    message: 'Compare price should be higher than regular price',
    path: ['compareAtPrice']
  }
).refine(
  (data) => {
    if (data.costPerItem && data.costPerItem > 0) {
      return data.costPerItem < data.price
    }
    return true
  },
  {
    message: 'Cost should be lower than selling price for profit',
    path: ['costPerItem']
  }
)

type PricingFormData = z.infer<typeof pricingFormSchema>

export default function ProductPricingEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })
  const { updateProduct, loading: isUpdating } = useProductMutations()
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  const {
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting, isDirty }
  } = useForm<PricingFormData>({
    resolver: zodResolver(pricingFormSchema),
    mode: 'onChange',
    defaultValues: {
      price: 0,
      compareAtPrice: null,
      costPerItem: null,
    },
  })

  // Update form when product loads
  useEffect(() => {
    if (product) {
      setValue('price', product.price?.amount || 0)
      setValue('compareAtPrice', product.compareAtPrice?.amount || null)
      setValue('costPerItem', product.costPerItem?.amount || null)
    }
  }, [product, setValue])

  const onSubmit = async (data: PricingFormData) => {
    if (!product) return

    try {
      const updateData = {
        id: product.id,
        price: data.price,
        compareAtPrice: data.compareAtPrice && data.compareAtPrice > 0 ? data.compareAtPrice : undefined,
        costPerItem: data.costPerItem && data.costPerItem > 0 ? data.costPerItem : undefined,
        currency: 'ZAR',
      }

      const result = await updateProduct(updateData as any)
      if (result) {
        toast.success('Pricing updated successfully')
        router.push(`/admin/e-commerce/products/${productId}`)
      }
    } catch (error) {
      toast.error('Failed to update pricing')
      console.error('Error updating product pricing:', error)
    }
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Loading...</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="h-8 bg-muted animate-pulse rounded" />
              <div className="h-32 bg-muted animate-pulse rounded" />
              <div className="h-8 bg-muted animate-pulse rounded" />
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Error</h1>
          </div>
        </div>
        <Card>
          <CardContent className="pt-6">
            <p className="text-red-500">{error?.message || 'Product not found'}</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Calculate profit metrics
  const currentPrice = watch('price') || 0
  const currentCost = watch('costPerItem') || 0
  const currentComparePrice = watch('compareAtPrice') || 0

  const profit = currentPrice - currentCost
  const profitMargin = currentPrice > 0 ? (profit / currentPrice) * 100 : 0
  const markup = currentCost > 0 ? (profit / currentCost) * 100 : 0
  const discount = currentComparePrice > 0 ? ((currentComparePrice - currentPrice) / currentComparePrice) * 100 : 0

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href={`/admin/e-commerce/products/${productId}/edit`}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Edit
            </Link>
          </Button>
          <div>
            <div className="flex items-center space-x-2">
              <h1 className="text-3xl font-bold tracking-tight">Pricing</h1>
              <Badge variant={product.status === 'active' ? 'default' : 'secondary'}>
                {product.status}
              </Badge>
            </div>
            <p className="text-muted-foreground">{product.title}</p>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => setIsPreviewMode(!isPreviewMode)}
          >
            <Eye className="mr-2 h-4 w-4" />
            {isPreviewMode ? 'Edit' : 'Preview'}
          </Button>
          <Button
            type="submit"
            disabled={isUpdating || isSubmitting || !isDirty}
            className="min-w-[140px]"
          >
            {(isUpdating || isSubmitting) && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            <Save className="mr-2 h-4 w-4" />
            Save Changes
          </Button>
        </div>
      </div>

      {/* Pricing Alerts */}
      {currentCost > 0 && currentPrice > 0 && profit <= 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Warning: Your selling price is lower than or equal to your cost. You're not making a profit on this product.
          </AlertDescription>
        </Alert>
      )}

      {isDirty && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            You have unsaved pricing changes. Don't forget to save your work.
          </AlertDescription>
        </Alert>
      )}

      {/* Form Content */}
      {!isPreviewMode ? (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="mr-2 h-5 w-5" />
                Pricing Information
              </CardTitle>
              <CardDescription>
                Set your product prices in South African Rand (ZAR). All prices include VAT where applicable.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <ZarPriceInput
                  label="Regular Price *"
                  value={watch('price')}
                  onChange={(value) => setValue('price', value, { shouldValidate: true })}
                  required
                  description="The standard selling price customers will pay"
                  error={errors.price?.message}
                />

                <ZarPriceInput
                  label="Compare at Price"
                  value={watch('compareAtPrice') || 0}
                  onChange={(value) => setValue('compareAtPrice', value > 0 ? value : null, { shouldValidate: true })}
                  description="Original price for comparison (shows discount)"
                  error={errors.compareAtPrice?.message}
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <ZarPriceInput
                  label="Cost per Item"
                  value={watch('costPerItem') || 0}
                  onChange={(value) => setValue('costPerItem', value > 0 ? value : null, { shouldValidate: true })}
                  description="Your cost for this item (for profit calculations)"
                  error={errors.costPerItem?.message}
                />

                {/* Quick Pricing Tools */}
                <div className="space-y-4">
                  <Label>Quick Pricing Tools</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (currentCost > 0) {
                          setValue('price', currentCost * 2, { shouldValidate: true })
                        }
                      }}
                      disabled={!currentCost}
                    >
                      2x Cost
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (currentCost > 0) {
                          setValue('price', currentCost * 2.5, { shouldValidate: true })
                        }
                      }}
                      disabled={!currentCost}
                    >
                      2.5x Cost
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (currentPrice > 0) {
                          setValue('compareAtPrice', currentPrice * 1.2, { shouldValidate: true })
                        }
                      }}
                      disabled={!currentPrice}
                    >
                      +20% Compare
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (currentPrice > 0) {
                          setValue('compareAtPrice', currentPrice * 1.5, { shouldValidate: true })
                        }
                      }}
                      disabled={!currentPrice}
                    >
                      +50% Compare
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Profit Analytics */}
          {(currentPrice > 0 || currentCost > 0) && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calculator className="mr-2 h-5 w-5" />
                  Profit Analytics
                </CardTitle>
                <CardDescription>
                  Real-time profit calculations and pricing insights
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  {/* Profit Summary */}
                  <div className="space-y-4">
                    <h4 className="font-medium">Profit Summary</h4>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Selling Price:</span>
                        <span className="font-medium">R {currentPrice.toFixed(2)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Cost:</span>
                        <span className="font-medium">R {currentCost.toFixed(2)}</span>
                      </div>
                      <Separator />
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Profit:</span>
                        <span className={cn(
                          "font-medium",
                          profit > 0 ? "text-green-600" : profit < 0 ? "text-red-600" : "text-muted-foreground"
                        )}>
                          R {profit.toFixed(2)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-muted-foreground">Margin:</span>
                        <span className={cn(
                          "font-medium",
                          profitMargin > 0 ? "text-green-600" : profitMargin < 0 ? "text-red-600" : "text-muted-foreground"
                        )}>
                          {profitMargin.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Discount Analysis */}
                  {currentComparePrice > 0 && (
                    <div className="space-y-4">
                      <h4 className="font-medium">Discount Analysis</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Compare Price:</span>
                          <span className="font-medium">R {currentComparePrice.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Selling Price:</span>
                          <span className="font-medium">R {currentPrice.toFixed(2)}</span>
                        </div>
                        <Separator />
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Discount:</span>
                          <span className="font-medium text-orange-600">
                            {discount.toFixed(1)}% off
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Savings:</span>
                          <span className="font-medium text-green-600">
                            R {(currentComparePrice - currentPrice).toFixed(2)}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Markup Analysis */}
                  {currentCost > 0 && (
                    <div className="space-y-4">
                      <h4 className="font-medium">Markup Analysis</h4>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Cost:</span>
                          <span className="font-medium">R {currentCost.toFixed(2)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-muted-foreground">Markup:</span>
                          <span className={cn(
                            "font-medium",
                            markup > 0 ? "text-green-600" : "text-muted-foreground"
                          )}>
                            {markup.toFixed(1)}%
                          </span>
                        </div>
                        <Separator />
                        <div className="text-xs text-muted-foreground">
                          Industry average markup is typically 50-100%
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      ) : (
        /* Preview Mode */
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Eye className="mr-2 h-5 w-5" />
              Pricing Preview
            </CardTitle>
            <CardDescription>
              How your pricing will appear to customers
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="border rounded-lg p-6 bg-muted/20">
              <div className="space-y-4">
                <h3 className="text-xl font-semibold">{product.title}</h3>

                <div className="flex items-center space-x-4">
                  <div className="text-3xl font-bold">
                    R {currentPrice.toFixed(2)}
                  </div>
                  {currentComparePrice > 0 && (
                    <div className="flex items-center space-x-2">
                      <span className="text-lg text-muted-foreground line-through">
                        R {currentComparePrice.toFixed(2)}
                      </span>
                      <Badge variant="destructive">
                        {discount.toFixed(0)}% OFF
                      </Badge>
                    </div>
                  )}
                </div>

                {currentComparePrice > 0 && (
                  <div className="text-sm text-green-600">
                    You save R {(currentComparePrice - currentPrice).toFixed(2)}
                  </div>
                )}

                <div className="text-sm text-muted-foreground">
                  Price includes VAT where applicable
                </div>
              </div>
            </div>

            {/* Pricing Insights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <h4 className="font-medium">Pricing Strategy</h4>
                <div className="text-sm text-muted-foreground space-y-1">
                  {profitMargin > 50 && <p>✅ Healthy profit margin</p>}
                  {profitMargin > 0 && profitMargin <= 50 && <p>⚠️ Moderate profit margin</p>}
                  {profitMargin <= 0 && <p>❌ No profit or loss</p>}
                  {discount > 20 && <p>🎯 Attractive discount for customers</p>}
                  {markup > 100 && <p>📈 High markup - premium positioning</p>}
                </div>
              </div>

              <div className="space-y-2">
                <h4 className="font-medium">Recommendations</h4>
                <div className="text-sm text-muted-foreground space-y-1">
                  {profitMargin < 20 && <p>Consider increasing price for better margins</p>}
                  {!currentComparePrice && <p>Add compare price to show value</p>}
                  {currentCost === 0 && <p>Add cost data for profit tracking</p>}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </form>
  )
}
