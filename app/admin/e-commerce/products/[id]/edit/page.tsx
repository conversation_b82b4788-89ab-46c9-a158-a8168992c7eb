'use client'

import { usePara<PERSON>, useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { ArrowLeft, Package, DollarSign, Archive, Palette, Image, Search } from 'lucide-react'
import Link from 'next/link'
import { useProduct } from '@/lib/ecommerce/hooks/use-products'
import { ProductForm } from '@/components/admin/products/product-form'
import { toast } from 'sonner'

export default function ProductEditPage() {
  const params = useParams()
  const router = useRouter()
  const productId = params.id as string

  const { product, loading, error } = useProduct({ productId })

  const handleCancel = () => {
    router.push(`/admin/e-commerce/products/${productId}`)
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
        </div>
        <div className="grid gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="h-20 bg-gray-200 rounded animate-pulse"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/products">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Products
            </Link>
          </Button>
        </div>
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Product not found</h3>
              <p className="mt-1 text-sm text-gray-500">
                The product you're trying to edit doesn't exist or has been deleted.
              </p>
              <div className="mt-6">
                <Button asChild>
                  <Link href="/admin/products">
                    Back to Products
                  </Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button variant="ghost" size="sm" asChild>
          <Link href="/admin/e-commerce/products">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Products
          </Link>
        </Button>
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Edit Product</h1>
          <p className="text-muted-foreground">{product.title}</p>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b">
        <nav className="flex space-x-8">
          <Link
            href={`/admin/e-commerce/products/${productId}/edit/general`}
            className="flex items-center py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            <Package className="mr-2 h-4 w-4" />
            General
          </Link>
          <Link
            href={`/admin/e-commerce/products/${productId}/edit/pricing`}
            className="flex items-center py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            <DollarSign className="mr-2 h-4 w-4" />
            Pricing
          </Link>
          <Link
            href={`/admin/e-commerce/products/${productId}/edit/inventory`}
            className="flex items-center py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            <Archive className="mr-2 h-4 w-4" />
            Inventory
          </Link>
          <Link
            href={`/admin/e-commerce/products/${productId}/edit/variants`}
            className="flex items-center py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            <Palette className="mr-2 h-4 w-4" />
            Variants
          </Link>
          <Link
            href={`/admin/e-commerce/products/${productId}/edit/media`}
            className="flex items-center py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            <Image className="mr-2 h-4 w-4" />
            Media
          </Link>
          <Link
            href={`/admin/e-commerce/products/${productId}/edit/seo`}
            className="flex items-center py-2 px-1 border-b-2 border-transparent text-sm font-medium text-gray-500 hover:text-gray-700 hover:border-gray-300"
          >
            <Search className="mr-2 h-4 w-4" />
            SEO
          </Link>
        </nav>
      </div>

      {/* Default content - show the full form */}
      <ProductForm
        product={product}
        onSuccess={() => {
          toast.success('Product updated successfully')
          router.push(`/admin/e-commerce/products/${productId}`)
        }}
        onCancel={handleCancel}
      />
    </div>
  )
}
