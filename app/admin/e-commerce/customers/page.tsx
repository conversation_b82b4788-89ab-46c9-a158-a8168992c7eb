'use client'

import { useRouter } from 'next/navigation'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbPage, BreadcrumbSeparator } from '@/components/ui/breadcrumb'
import { useCustomers } from '@/lib/ecommerce/hooks/use-customers'
import { EnhancedCustomerList } from '@/components/admin/customers/enhanced-customer-list'

export default function CustomersPage() {
  const router = useRouter()
  const { customers, loading, pagination, searchCustomers } = useCustomers()

  const handleCreateCustomer = () => {
    router.push('/admin/customers/new')
  }

  const handleEditCustomer = (customer: any) => {
    router.push(`/admin/customers/${customer.id}/edit`)
  }

  const handleViewCustomer = (customer: any) => {
    router.push(`/admin/customers/${customer.id}`)
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumbs */}
      <Breadcrumb>
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/admin">Admin</BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbPage>Customers</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customers</h1>
          <p className="text-muted-foreground">
            Manage customer relationships and track loyalty
          </p>
        </div>
      </div>

      {/* Enhanced Customer List */}
      <EnhancedCustomerList
        onCreateCustomer={handleCreateCustomer}
        onEditCustomer={handleEditCustomer}
        onViewCustomer={handleViewCustomer}
      />
    </div>
  )
}
