import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { join, dirname } from 'path'
import { existsSync } from 'fs'
import { nanoid } from 'nanoid'

// Constants
const UPLOADS_DIR = join(process.cwd(), 'public', 'uploads', 'products')
const MAX_FILE_SIZE = 5 * 1024 * 1024 // 5MB
const ALLOWED_FILE_TYPES = [
  'image/jpeg',
  'image/png',
  'image/webp',
  'image/gif'
]

// Create uploads directory if it doesn't exist
async function ensureUploadsDirectory() {
  if (!existsSync(UPLOADS_DIR)) {
    await mkdir(UPLOADS_DIR, { recursive: true })
  }
}

export async function POST(request: NextRequest) {
  try {
    // Ensure the upload directory exists
    await ensureUploadsDirectory()

    // Process form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      )
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        { success: false, error: 'File size exceeds the 5MB limit' },
        { status: 400 }
      )
    }

    // Validate file type
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Invalid file type. Only JPEG, PNG, WebP, and GIF are allowed' },
        { status: 400 }
      )
    }

    // Generate a unique filename to prevent collisions
    const fileExt = file.name.split('.').pop() || 'jpg'
    const fileName = `${nanoid()}.${fileExt}`
    const filePath = join(UPLOADS_DIR, fileName)
    
    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())
    
    // Save the file
    await writeFile(filePath, buffer)
    
    // Return the URL to the uploaded file
    const fileUrl = `/uploads/products/${fileName}`
    
    // Get basic image dimensions
    // In a production app, you would use a library like sharp to get accurate dimensions
    // and possibly generate thumbnails and optimized versions
    const dimensions = {
      width: 800, // placeholder
      height: 600 // placeholder
    }

    return NextResponse.json({ 
      success: true, 
      data: { 
        url: fileUrl,
        width: dimensions.width,
        height: dimensions.height,
        filename: fileName,
        originalName: file.name,
        size: file.size,
        mimeType: file.type
      } 
    })
  } catch (error) {
    console.error('Error uploading file:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to upload file' 
      },
      { status: 500 }
    )
  }
}

// Increase body limit for file uploads
export const config = {
  api: {
    bodyParser: false,
    responseLimit: '10mb',
    maxDuration: 60 // 60 seconds timeout
  }
} 