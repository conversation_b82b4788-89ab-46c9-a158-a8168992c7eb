// API route to update payment status for an order
// PATCH /api/e-commerce/orders/[id]/payment-status

import { NextRequest, NextResponse } from 'next/server'
import { OrderService } from '@/lib/ecommerce/services/order-service'

const orderService = new OrderService()

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params
    const body = await request.json()
    
    if (!body.status) {
      return NextResponse.json(
        { success: false, error: 'Payment status is required' },
        { status: 400 }
      )
    }

    // Make sure the status is valid
    const validStatuses = [
      'pending', 
      'authorized', 
      'partially_paid', 
      'paid', 
      'partially_refunded', 
      'refunded', 
      'voided'
    ]
    
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json(
        { success: false, error: 'Invalid payment status' },
        { status: 400 }
      )
    }
    
    // Update the order's payment status
    const updatedOrder = await orderService.updatePaymentStatus(id, body.status)
    
    if (!updatedOrder) {
      return NextResponse.json(
        { success: false, error: `Order with ID ${id} not found or could not be updated` },
        { status: 404 }
      )
    }
    
    return NextResponse.json({
      success: true,
      data: updatedOrder
    })
    
  } catch (error) {
    console.error('Update payment status error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to update payment status' 
      },
      { status: 500 }
    )
  }
} 