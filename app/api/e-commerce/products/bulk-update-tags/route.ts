import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.productIds || !Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs are required' },
        { status: 400 }
      )
    }
    
    if (!body.tags || !Array.isArray(body.tags) || body.tags.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Tags are required' },
        { status: 400 }
      )
    }
    
    // Process each product
    const updatedProducts = []
    const errors = []
    
    for (const productId of body.productIds) {
      try {
        // Get current product to append tags
        const productResult = await productService().getProductById(productId)
        
        if (!productResult.success || !productResult.data) {
          errors.push(`Product ${productId} not found`)
          continue
        }
        
        // Create tagIds from tag names
        // In a real implementation, you might need to first create the tags if they don't exist
        // For simplicity, we're assuming the tags already exist with IDs that match the names
        const existingTagNames = productResult.data.tags?.map(tag => tag.name) || []
        const uniqueTagNames = [...new Set([...existingTagNames, ...body.tags])]
        
        // Update product with tags
        const updateResult = await productService().updateProduct({
          id: productId,
          tags: uniqueTagNames // This is a simplified version; in reality, you'd need tag IDs
        })
        
        if (updateResult.success) {
          updatedProducts.push(productId)
        } else {
          errors.push(`Failed to update product ${productId}: ${updateResult.error?.message || 'Unknown error'}`)
        }
      } catch (error) {
        errors.push(`Error updating product ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    
    return NextResponse.json({
      success: errors.length === 0,
      data: {
        updatedCount: updatedProducts.length,
        skippedCount: body.productIds.length - updatedProducts.length,
        errors: errors.length > 0 ? errors : undefined
      }
    })
  } catch (error) {
    console.error('Bulk update tags API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
} 