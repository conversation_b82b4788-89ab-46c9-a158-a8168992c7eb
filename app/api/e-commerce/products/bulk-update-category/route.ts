import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.productIds || !Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs are required' },
        { status: 400 }
      )
    }
    
    if (!body.categoryId) {
      return NextResponse.json(
        { success: false, error: 'Category ID is required' },
        { status: 400 }
      )
    }
    
    // Process each product
    const updatedProducts = []
    const errors = []
    
    for (const productId of body.productIds) {
      try {
        // Update product
        const updateResult = await productService().updateProduct({
          id: productId,
          categoryIds: [body.categoryId] // Replace existing categories with the new one
        })
        
        if (updateResult.success) {
          updatedProducts.push(productId)
        } else {
          errors.push(`Failed to update product ${productId}: ${updateResult.error?.message || 'Unknown error'}`)
        }
      } catch (error) {
        errors.push(`Error updating product ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    
    return NextResponse.json({
      success: errors.length === 0,
      data: {
        updatedCount: updatedProducts.length,
        skippedCount: body.productIds.length - updatedProducts.length,
        errors: errors.length > 0 ? errors : undefined
      }
    })
  } catch (error) {
    console.error('Bulk update category API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
} 