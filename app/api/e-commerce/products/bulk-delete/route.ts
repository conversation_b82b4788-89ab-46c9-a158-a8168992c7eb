import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.productIds || !Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs are required' },
        { status: 400 }
      )
    }
    
    // Optional parameters
    const options = {
      force: body.force === true, // Force deletion even with dependencies
      reason: body.reason || 'Bulk deletion requested by user'
    }
    
    // Call service to delete products
    const result = await productService().bulkDeleteProducts(body.productIds, options)
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          deletedCount: result.data?.deletedCount || 0,
          skippedCount: result.data?.skippedCount || 0,
          errors: result.data?.errors || []
        }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Bulk delete products API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
} 