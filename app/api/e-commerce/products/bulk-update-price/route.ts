import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.productIds || !Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs are required' },
        { status: 400 }
      )
    }
    
    if (!body.adjustmentType || !['percentage', 'fixed'].includes(body.adjustmentType)) {
      return NextResponse.json(
        { success: false, error: 'Valid adjustment type (percentage or fixed) is required' },
        { status: 400 }
      )
    }
    
    if (typeof body.adjustmentValue !== 'number') {
      return NextResponse.json(
        { success: false, error: 'Adjustment value must be a number' },
        { status: 400 }
      )
    }
    
    // Get all products first to calculate new prices
    const updatedProducts = []
    const errors = []
    
    for (const productId of body.productIds) {
      try {
        // Get product
        const productResult = await productService().getProductById(productId)
        
        if (!productResult.success || !productResult.data) {
          errors.push(`Product ${productId} not found`)
          continue
        }
        
        const product = productResult.data
        
        // Calculate new price
        let newPrice
        
        if (body.adjustmentType === 'percentage') {
          // Percentage adjustment
          newPrice = product.price.amount * (1 + body.adjustmentValue)
        } else {
          // Fixed amount adjustment
          newPrice = product.price.amount + body.adjustmentValue
        }
        
        // Ensure price is not negative
        newPrice = Math.max(newPrice, 0.01)
        
        // Update product
        const updateResult = await productService().updateProduct({
          id: productId,
          price: {
            amount: newPrice,
            currency: product.price.currency
          }
        })
        
        if (updateResult.success) {
          updatedProducts.push(productId)
        } else {
          errors.push(`Failed to update product ${productId}: ${updateResult.error?.message || 'Unknown error'}`)
        }
      } catch (error) {
        errors.push(`Error updating product ${productId}: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }
    
    return NextResponse.json({
      success: errors.length === 0,
      data: {
        updatedCount: updatedProducts.length,
        skippedCount: body.productIds.length - updatedProducts.length,
        errors: errors.length > 0 ? errors : undefined
      }
    })
  } catch (error) {
    console.error('Bulk update price API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
} 