import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate required fields
    if (!body.productIds || !Array.isArray(body.productIds) || body.productIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Product IDs are required' },
        { status: 400 }
      )
    }
    
    if (!body.status || !['active', 'draft', 'archived'].includes(body.status)) {
      return NextResponse.json(
        { success: false, error: 'Valid status (active, draft, or archived) is required' },
        { status: 400 }
      )
    }
    
    // Call service to update statuses
    const result = await productService().bulkUpdateStatus(body.productIds, body.status)
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        data: {
          updatedCount: result.data?.updatedCount || 0,
          skippedCount: body.productIds.length - (result.data?.updatedCount || 0)
        }
      })
    } else {
      const error = handleEcommerceError(result.error)
      return NextResponse.json(
        { success: false, error: error.message },
        { status: error.statusCode }
      )
    }
  } catch (error) {
    console.error('Bulk update status API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
} 