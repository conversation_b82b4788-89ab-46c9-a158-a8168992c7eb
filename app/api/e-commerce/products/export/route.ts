import { NextRequest, NextResponse } from 'next/server'
import { productService, handleEcommerceError } from '@/lib/ecommerce'
import { Product } from '@/lib/ecommerce/types'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Get product IDs from query params (can be multiple)
    const productIds = searchParams.getAll('ids')
    
    // If no specific IDs are provided, we'll export based on filters
    if (productIds.length === 0) {
      // Parse filter parameters similar to the regular product search endpoint
      const filters = {
        status: searchParams.get('status') ? searchParams.get('status')!.split(',') as any : undefined,
        categoryIds: searchParams.get('categoryIds') ? searchParams.get('categoryIds')!.split(',') : undefined,
        tagIds: searchParams.get('tagIds') ? searchParams.get('tagIds')!.split(',') : undefined,
      }
      
      // Get products based on filters
      const result = await productService().searchProducts({
        filters,
        limit: 1000 // Cap at 1000 products for export to avoid timeouts
      })
      
      if (!result.success) {
        const error = handleEcommerceError(result.error)
        return NextResponse.json(
          { success: false, error: error.message },
          { status: error.statusCode }
        )
      }
      
      return generateCsvResponse(result.data?.data || [])
    } else {
      // Get specific products by ID
      const products: Product[] = []
      
      for (const id of productIds) {
        const result = await productService().getProductById(id)
        if (result.success && result.data) {
          products.push(result.data)
        }
      }
      
      return generateCsvResponse(products)
    }
  } catch (error) {
    console.error('Export products API error:', error)
    const errorResponse = handleEcommerceError(error)
    return NextResponse.json(
      { success: false, error: errorResponse.message },
      { status: errorResponse.statusCode }
    )
  }
}

// Helper function to generate CSV from products
function generateCsvResponse(products: Product[]): NextResponse {
  // Define CSV headers
  const headers = [
    'id',
    'title',
    'description',
    'slug',
    'status',
    'price',
    'compareAtPrice',
    'inventoryQuantity',
    'vendor',
    'productType',
    'createdAt',
    'updatedAt',
    'tags',
    'categories',
    'images'
  ]
  
  // Generate CSV rows
  const rows = products.map(product => {
    return [
      product.id,
      `"${(product.title || '').replace(/"/g, '""')}"`, // Escape quotes in CSV
      `"${(product.description || '').replace(/"/g, '""')}"`,
      product.slug,
      product.status,
      product.price?.amount || 0,
      product.compareAtPrice?.amount || '',
      product.inventoryQuantity || 0,
      product.vendor || '',
      product.productType || '',
      new Date(product.createdAt).toISOString(),
      new Date(product.updatedAt).toISOString(),
      product.tags?.map(tag => tag.name).join('|') || '',
      product.categories?.map(c => c.category.name).join('|') || '',
      product.images?.map(img => img.url).join('|') || ''
    ].join(',')
  })
  
  // Create CSV content
  const csv = [
    headers.join(','),
    ...rows
  ].join('\n')
  
  // Return CSV response with appropriate headers
  return new NextResponse(csv, {
    headers: {
      'Content-Type': 'text/csv',
      'Content-Disposition': `attachment; filename=products-export-${new Date().toISOString().split('T')[0]}.csv`
    }
  })
} 