// E-commerce types

export interface Customer {
  id?: string
  firstName: string
  lastName: string
  email: string
  phone?: string
}

export interface Address {
  firstName: string
  lastName: string
  company?: string
  address1: string
  address2?: string
  city: string
  state?: string
  province?: string
  postalCode: string
  country: string
  phone?: string
}

// Product Types
export interface Product {
  id: string
  title: string
  description: string
  descriptionHtml?: string
  slug: string
  price: {
    amount: number
    currency: string
  }
  compareAtPrice?: {
    amount: number
    currency: string
  }
  costPerItem?: {
    amount: number
    currency: string
  }
  inventoryQuantity: number
  trackQuantity: boolean
  continueSellingWhenOutOfStock: boolean
  status: 'active' | 'draft' | 'archived'
  vendor?: string
  productType?: string
  weight?: number
  weightUnit?: string
  dimensions?: {
    length: number
    width: number
    height: number
    unit: string
  }
  variants?: ProductVariant[]
  options?: ProductOption[]
  images?: ProductImage[]
  tags?: { id: string, name: string }[]
  categories?: { category: { id: string, name: string } }[]
  collections?: { collection: { id: string, name: string } }[]
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
  isGiftCard?: boolean
  requiresShipping?: boolean
  isTaxable?: boolean
  isVisible: boolean
  createdAt: string | Date
  updatedAt: string | Date
}

export interface ProductVariant {
  id?: string
  sku?: string
  title: string
  price: {
    amount: number
    currency: string
  }
  compareAtPrice?: {
    amount: number
    currency: string
  }
  costPerItem?: {
    amount: number
    currency: string
  }
  inventoryQuantity: number
  inventoryPolicy?: string
  fulfillmentService?: string
  inventoryManagement?: boolean
  available: boolean
  weight?: number
  weightUnit?: string
  options?: ProductVariantOption[]
  position?: number
  barcode?: string
  taxable?: boolean
  requiresShipping?: boolean
  trackQuantity?: boolean
  continueSellingWhenOutOfStock?: boolean
  metafields?: Record<string, any>
}

export interface ProductVariantOption {
  id?: string
  name: string
  value: string
}

export interface ProductOption {
  id?: string
  name: string
  position: number
  values: string[]
}

export interface ProductImage {
  id?: string
  url: string
  altText: string
  position: number
  width?: number
  height?: number
}

export interface ProductSortOptions {
  field: 'title' | 'price' | 'createdAt' | 'updatedAt' | 'inventoryQuantity' | 'averageRating' | string
  direction: 'asc' | 'desc'
}

export interface CreateProductInput {
  title: string
  description: string
  descriptionHtml?: string
  slug?: string
  vendor?: string
  productType?: string
  price: {
    amount: number
    currency: string
  }
  compareAtPrice?: {
    amount: number
    currency: string
  }
  costPerItem?: {
    amount: number
    currency: string
  }
  trackQuantity?: boolean
  continueSellingWhenOutOfStock?: boolean
  inventoryQuantity?: number
  weight?: number
  weightUnit?: string
  dimensions?: {
    length: number
    width: number
    height: number
    unit: string
  }
  images?: ProductImage[]
  variants?: Omit<ProductVariant, 'id'>[]
  options?: Omit<ProductOption, 'id'>[]
  categoryIds?: string[]
  tagIds?: string[]
  collectionIds?: string[]
  seo?: {
    title?: string
    description?: string
    keywords?: string[]
  }
  metafields?: Record<string, any>
  isGiftCard?: boolean
  requiresShipping?: boolean
  isTaxable?: boolean
  status?: 'active' | 'draft' | 'archived'
  isVisible?: boolean
}

export interface UpdateProductInput extends Partial<CreateProductInput> {
  id: string
}

export interface ProductSearchParams {
  query?: string
  page?: number
  limit?: number
  filters?: ProductFilters
  sort?: ProductSortOptions
}

export interface ProductFilters {
  status?: ('active' | 'draft' | 'archived')[]
  categoryIds?: string[]
  tagIds?: string[]
  collectionIds?: string[]
  vendor?: string
  productType?: string
  inStock?: boolean
  onSale?: boolean
  isVisible?: boolean
  priceRange?: {
    min?: number
    max?: number
  }
}

export interface OrderItem {
  id?: string
  productId: string
  variantId?: string
  name?: string
  quantity: number
  unitPrice: {
    amount: number
    currency?: string
  }
  image?: string
  customAttributes?: Record<string, any>
  personalizedMessage?: string
  giftWrap?: boolean
}

export interface Order {
  id: string
  orderNumber: string
  customer?: Customer
  customerEmail?: string
  customerPhone?: string
  status: string
  paymentStatus: string
  items?: OrderItem[]
  total: number | { amount: number; currency?: string }
  subtotal?: number
  tax?: number
  shipping?: number
  discount?: number
  currency?: string
  shippingAddress?: Address
  billingAddress?: Address
  shippingMethod?: {
    id: string
    name: string
    price: number
  }
  createdAt: string | Date
  updatedAt?: string | Date
  attributes?: Record<string, any>
  tags?: string[]
  notes?: string[]
  customerNote?: string
  internalNotes?: string[]
}

export interface CreateOrderInput {
  customerId?: string
  email: string
  currency: string
  items: Array<{
    productId: string
    variantId?: string
    quantity: number
    price: number
  }>
  shippingAddress?: Omit<Address, 'id'>
  billingAddress?: Omit<Address, 'id'>
  shippingMethod?: string
  notes?: string
  tags?: string[]
  metadata?: Record<string, any>
}

export interface UpdateOrderInput {
  id: string
  customerId?: string
  email?: string
  status?: string
  fulfillmentStatus?: string
  paymentStatus?: string
  shippingAddress?: Omit<Address, 'id'>
  billingAddress?: Omit<Address, 'id'>
  notes?: string
  tags?: string[]
  metadata?: Record<string, any>
}

export interface OrderSearchParams {
  page?: number
  limit?: number
  query?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filters?: {
    status?: string[]
    paymentStatus?: string[]
    customerId?: string
    startDate?: Date
    endDate?: Date
  }
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
  success?: boolean
  error?: string
}

// Error Types
export class NotFoundError extends Error {
  constructor(message = 'Resource not found') {
    super(message)
    this.name = 'NotFoundError'
  }
}

export class ValidationError extends Error {
  constructor(message = 'Validation error') {
    super(message)
    this.name = 'ValidationError'
  }
}

export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
  }
}

export interface CreateVariantInput {
  title: string
  sku?: string
  price: {
    amount: number
    currency: string
  }
  compareAtPrice?: {
    amount: number
    currency: string
  }
  costPerItem?: {
    amount: number
    currency: string
  }
  inventoryQuantity?: number
  inventoryPolicy?: string
  fulfillmentService?: string
  inventoryManagement?: boolean
  weight?: number
  weightUnit?: string
  options?: ProductVariantOption[]
  barcode?: string
  taxable?: boolean
  requiresShipping?: boolean
  trackQuantity?: boolean
  continueSellingWhenOutOfStock?: boolean
  metafields?: Record<string, any>
}