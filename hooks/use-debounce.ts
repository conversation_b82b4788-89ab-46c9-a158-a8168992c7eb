import { useState, useEffect } from 'react'

/**
 * A hook that returns a debounced value that only updates after the specified delay
 * @param value The value to debounce
 * @param delay The delay in milliseconds
 * @returns The debounced value
 */
export function useDebounce<T>(value: T, delay = 500): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    // Set up timeout to update debounced value after delay
    const timer = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    // Clean up timeout if value changes within delay period
    return () => {
      clearTimeout(timer)
    }
  }, [value, delay])

  return debouncedValue
} 