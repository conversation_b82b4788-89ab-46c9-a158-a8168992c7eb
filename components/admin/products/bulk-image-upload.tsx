'use client'

import { useState, useRef } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Progress } from '@/components/ui/progress'
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  AlertTriangle, 
  CheckCircle 
} from 'lucide-react'
import { toast } from 'sonner'
import { ProductImage } from '@/lib/ecommerce/types'

interface BulkImageUploadProps {
  onImagesUploaded: (images: ProductImage[]) => void
  maxImages?: number
}

export function BulkImageUpload({ 
  onImagesUploaded,
  maxImages = 10 
}: BulkImageUploadProps) {
  const [files, setFiles] = useState<File[]>([])
  const [uploading, setUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [errors, setErrors] = useState<{ file: string, error: string }[]>([])
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files || e.target.files.length === 0) return
    
    // Convert FileList to array
    const fileArray = Array.from(e.target.files)
    
    // Validate file count
    if (fileArray.length + files.length > maxImages) {
      toast.error(`You can upload a maximum of ${maxImages} images`)
      return
    }
    
    // Validate file types and sizes
    const validFiles: File[] = []
    const invalidFiles: { file: string, error: string }[] = []
    
    fileArray.forEach(file => {
      // Check file type
      if (!file.type.startsWith('image/')) {
        invalidFiles.push({ file: file.name, error: 'Not an image file' })
        return
      }
      
      // Check file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        invalidFiles.push({ file: file.name, error: 'File exceeds 5MB size limit' })
        return
      }
      
      validFiles.push(file)
    })
    
    // Update state
    setFiles(prev => [...prev, ...validFiles])
    setErrors(prev => [...prev, ...invalidFiles])
    
    // Reset the file input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
    
    // Show error toast if any invalid files
    if (invalidFiles.length > 0) {
      toast.error(`${invalidFiles.length} files couldn't be added`)
    }
  }
  
  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }
  
  const handleUpload = async () => {
    if (files.length === 0) {
      toast.error('No files to upload')
      return
    }
    
    setUploading(true)
    setProgress(0)
    setErrors([])
    
    try {
      const uploadedImages: ProductImage[] = []
      
      // Process each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i]
        
        try {
          // Create form data
          const formData = new FormData()
          formData.append('file', file)
          
          // Upload image to server
          const response = await fetch('/api/e-commerce/images/upload', {
            method: 'POST',
            body: formData,
          })
          
          if (!response.ok) {
            throw new Error(`Failed to upload ${file.name}`)
          }
          
          const imageData = await response.json()
          
          if (imageData.success && imageData.data) {
            uploadedImages.push({
              url: imageData.data.url,
              altText: file.name.split('.')[0], // Use filename as alt text
              position: i,
              width: imageData.data.width,
              height: imageData.data.height
            })
          } else {
            throw new Error(imageData.error || 'Unknown upload error')
          }
        } catch (error) {
          setErrors(prev => [...prev, { 
            file: file.name, 
            error: error instanceof Error ? error.message : 'Upload failed' 
          }])
        }
        
        // Update progress
        setProgress(((i + 1) / files.length) * 100)
      }
      
      // Return uploaded images
      if (uploadedImages.length > 0) {
        onImagesUploaded(uploadedImages)
        toast.success(`${uploadedImages.length} images uploaded successfully`)
        
        // Clear files if all uploads were successful
        if (uploadedImages.length === files.length) {
          setFiles([])
        }
      } else {
        toast.error('No images were uploaded successfully')
      }
    } catch (error) {
      console.error('Bulk upload error:', error)
      toast.error('An error occurred during upload')
    } finally {
      setUploading(false)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-2">
        <Label htmlFor="image-upload">Product Images</Label>
        <div className="flex items-center gap-4">
          <Input
            ref={fileInputRef}
            id="image-upload"
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileChange}
            disabled={uploading || files.length >= maxImages}
            className="flex-1"
          />
          <Button
            onClick={handleUpload}
            disabled={uploading || files.length === 0}
          >
            {uploading ? (
              <>Processing...</>
            ) : (
              <>
                <Upload className="mr-2 h-4 w-4" />
                Upload
              </>
            )}
          </Button>
        </div>
        <p className="text-sm text-muted-foreground">
          Upload up to {maxImages} product images (max 5MB each)
        </p>
      </div>
      
      {uploading && (
        <div className="space-y-2">
          <Progress value={progress} />
          <p className="text-sm text-center text-muted-foreground">
            Uploading {Math.round(progress)}%
          </p>
        </div>
      )}
      
      {files.length > 0 && (
        <Card>
          <CardContent className="p-4">
            <h3 className="text-sm font-medium mb-2">Files to upload ({files.length})</h3>
            <ScrollArea className="h-60">
              <div className="space-y-2">
                {files.map((file, index) => (
                  <div 
                    key={`${file.name}-${index}`}
                    className="flex items-center justify-between p-2 bg-muted rounded-md"
                  >
                    <div className="flex items-center gap-2 overflow-hidden">
                      <div className="w-10 h-10 bg-background rounded flex items-center justify-center">
                        <ImageIcon className="h-5 w-5 text-muted-foreground" />
                      </div>
                      <div className="truncate">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {(file.size / 1024).toFixed(1)} KB
                        </p>
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(index)}
                      disabled={uploading}
                    >
                      <X className="h-4 w-4" />
                      <span className="sr-only">Remove</span>
                    </Button>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
      
      {errors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <h3 className="text-sm font-medium mb-2 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
              Upload Errors ({errors.length})
            </h3>
            <ScrollArea className="h-40">
              <div className="space-y-2">
                {errors.map((error, index) => (
                  <div 
                    key={index}
                    className="flex items-center justify-between p-2 bg-white rounded-md"
                  >
                    <div className="flex items-center gap-2 overflow-hidden">
                      <div className="w-8 h-8 bg-red-100 text-red-600 rounded flex items-center justify-center">
                        <AlertTriangle className="h-4 w-4" />
                      </div>
                      <div className="truncate">
                        <p className="text-sm font-medium truncate">{error.file}</p>
                        <p className="text-xs text-red-500">{error.error}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 