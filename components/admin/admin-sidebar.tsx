"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState, useEffect } from "react"
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Warehouse,
  Users,
  Settings,
  LogOut,
  Store,
  TrendingUp,
  BarChart3,
  Bell,
  User,
  ChevronsUpDown,
  FileText,
  Palette,
  Shield,
  MessageSquare,
  HelpCircle,
  Tag,
  Layers,
  Sparkles,
  Database,
  Zap,
  Search,
  Home,
  Globe,
  Brush,
  Code,
  Monitor,
  Smartphone,
  Tablet,
  ChevronRight,
  Activity,
  DollarSign,
  ShoppingBag,
  UserCheck,
  Clock,
  Star,
  Heart,
  Eye,
  Download,
  Upload,
  RefreshCw,
  Trash2,
  Edit,
  Plus,
  Filter,
  SortAsc,
  Calendar,
  Mail,
  Phone,
  MapPin,
  CreditCard,
  Truck,
  Receipt,
  Lock,
  Key,
  Wifi,
  Cloud,
  Server,
  HardDrive,
  Workflow,
  Play,
  CheckCircle,
  FolderOpen
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
  SidebarRail,
} from "@/components/ui/sidebar"
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
// Removed useAdminUI dependency for simplified layout

// Hook to fetch order counts for sidebar badges
function useOrderCounts() {
  const [counts, setCounts] = useState({
    total: 0,
    pending: 0,
    processing: 0,
    shipped: 0,
    delivered: 0
  })

  useEffect(() => {
    const fetchCounts = async () => {
      try {
        const response = await fetch('/api/e-commerce/orders/stats?period=30d', {
          headers: { 'x-admin-request': 'true' }
        })
        const data = await response.json()

        if (data.success) {
          const stats = data.data
          setCounts({
            total: stats.overview?.totalOrders || 0,
            pending: stats.ordersByStatus?.pending || 0,
            processing: stats.ordersByStatus?.processing || 0,
            shipped: stats.ordersByStatus?.shipped || 0,
            delivered: stats.ordersByStatus?.delivered || 0
          })
        }
      } catch (error) {
        console.error('Failed to fetch order counts:', error)
      }
    }

    fetchCounts()
    // Refresh every 5 minutes
    const interval = setInterval(fetchCounts, 5 * 60 * 1000)
    return () => clearInterval(interval)
  }, [])

  return counts
}

// Generate orders navigation with dynamic counts
function getOrdersNavigation(orderCounts: ReturnType<typeof useOrderCounts>): NavigationItem {
  return {
    title: "Orders",
    url: "/admin/e-commerce/orders",
    icon: ShoppingCart,
    badge: orderCounts.total > 0 ? orderCounts.total.toString() : undefined,
    badgeVariant: orderCounts.pending > 5 ? "destructive" as const : "default" as const,
    description: "Process and manage orders",
    items: [
      {
        title: "All Orders",
        url: "/admin/e-commerce/orders",
        icon: ShoppingCart,
        description: "View all customer orders"
      },
      {
        title: "Processing Dashboard",
        url: "/admin/e-commerce/orders/processing",
        icon: Activity,
        badge: orderCounts.processing > 0 ? orderCounts.processing.toString() : undefined,
        description: "Order processing workflow dashboard"
      },
      {
        title: "Fulfillment Center",
        url: "/admin/e-commerce/orders/fulfillment",
        icon: Package,
        badge: orderCounts.pending > 0 ? orderCounts.pending.toString() : undefined,
        description: "Process order fulfillments"
      },
      {
        title: "Inventory Manager",
        url: "/admin/e-commerce/orders/inventory",
        icon: Warehouse,
        description: "Manage inventory for orders"
      },
      {
        title: "Automation Engine",
        url: "/admin/e-commerce/orders/automation",
        icon: Zap,
        description: "Automated order processing rules"
      },
      {
        title: "Pending",
        url: "/admin/e-commerce/orders?status=pending",
        icon: Clock,
        badge: orderCounts.pending > 0 ? orderCounts.pending.toString() : undefined,
        description: "Orders awaiting processing"
      },
      {
        title: "Processing",
        url: "/admin/e-commerce/orders?status=processing",
        icon: RefreshCw,
        badge: orderCounts.processing > 0 ? orderCounts.processing.toString() : undefined,
        description: "Orders being processed"
      },
      {
        title: "Shipped",
        url: "/admin/e-commerce/orders?status=shipped",
        icon: Truck,
        badge: orderCounts.shipped > 0 ? orderCounts.shipped.toString() : undefined,
        description: "Orders that have been shipped"
      },
      {
        title: "Delivered",
        url: "/admin/e-commerce/orders?status=delivered",
        icon: UserCheck,
        badge: orderCounts.delivered > 0 ? orderCounts.delivered.toString() : undefined,
        description: "Successfully delivered orders"
      },
      {
        title: "Create Order",
        url: "/admin/e-commerce/orders/new",
        icon: Plus,
        description: "Create a new order manually"
      },
    ],
  }
}

// Navigation data structure
interface NavigationItem {
  title: string
  url: string
  icon: any
  badge?: string
  badgeVariant?: "default" | "secondary" | "destructive" | "outline"
  description?: string
  items?: {
    title: string
    url: string
    icon?: any
    badge?: string
    description?: string
  }[]
}

const navigationData = {
  main: [
    {
      title: "Dashboard",
      url: "/admin",
      icon: LayoutDashboard,
      description: "Overview and key metrics",
    },
    {
      title: "Analytics",
      url: "/admin/analytics",
      icon: TrendingUp,
      badge: "New",
      badgeVariant: "secondary" as const,
      description: "Sales and performance insights",
    },
  ] as NavigationItem[],
  ecommerce: [
    {
      title: "Products",
      url: "/admin/e-commerce/products",
      icon: Package,
      description: "Manage your product catalog",
      items: [
        {
          title: "All Products",
          url: "/admin/e-commerce/products",
          icon: Package,
          description: "View and manage all products"
        },
        {
          title: "Enhanced View",
          url: "/admin/e-commerce/products/enhanced",
          icon: Sparkles,
          badge: "New",
          badgeVariant: "default" as const,
          description: "Advanced product management"
        },
        {
          title: "Add Product",
          url: "/admin/e-commerce/products/new",
          icon: Plus,
          description: "Create a new product"
        },
        {
          title: "Categories",
          url: "/admin/e-commerce/products/categories",
          icon: Tag,
          description: "Organize products by category"
        },
        {
          title: "Collections",
          url: "/admin/e-commerce/products/collections",
          icon: FolderOpen,
          description: "Manage product collections"
        },
        {
          title: "Analytics",
          url: "/admin/e-commerce/products/analytics",
          icon: BarChart3,
          description: "Product performance insights"
        },
      ],
    },
    // Orders section will be dynamically generated
    {
      title: "Fulfillments",
      url: "/admin/e-commerce/fulfillments",
      icon: Truck,
      description: "Manage order fulfillments and shipping",
    },
    {
      title: "Inventory",
      url: "/admin/e-commerce/inventory",
      icon: Warehouse,
      description: "Stock levels and inventory management",
    },
    {
      title: "Customers",
      url: "/admin/e-commerce/customers",
      icon: Users,
      description: "Customer management and insights",
    },
  ] as NavigationItem[],
  content: [
    {
      title: "Pages",
      url: "/admin/system/pages",
      icon: Layers,
      badgeVariant: "default" as const,
      description: "Manage site pages and content",
    },
    {
      title: "Layouts",
      url: "/admin/system/layouts",
      icon: Monitor,
      badgeVariant: "secondary" as const,
      description: "Design site layouts and themes",
    },
    {
      title: "Post Types",
      url: "/admin/system/post-types",
      icon: Layers,
      description: "Custom content types",
    },
    {
      title: "Taxonomies",
      url: "/admin/taxonomies",
      icon: Tag,
      description: "Content organization system",
    },
    {
      title: "Media",
      url: "/admin/system/media",
      icon: Database,
      description: "File and media management",
      items: [
        {
          title: "Media Library",
          url: "/admin/media",
          icon: Database,
          description: "Browse all uploaded files"
        },
        {
          title: "Upload Files",
          url: "/admin/media/upload",
          icon: Upload,
          description: "Upload new media files"
        },
        {
          title: "Storage Settings",
          url: "/admin/media/settings",
          icon: HardDrive,
          description: "Configure storage options"
        }
      ]
    },
  ] as NavigationItem[],
  reports: [
    {
      title: "Reports",
      url: "/admin/reports",
      icon: BarChart3,
      description: "Business intelligence and analytics",
    },
  ] as NavigationItem[],
  aiBuilders: [
    {
      title: "AI Builders Suite",
      url: "/admin/ai-builders",
      icon: Sparkles,
      badge: "New",
      badgeVariant: "default" as const,
      description: "AI-powered development tools",
      items: [
        {
          title: "AI Visual Editor",
          url: "/admin/ai-visual-editor",
          icon: Sparkles,
          description: "Generate React components with AI"
        },
        {
          title: "Next.js Generator",
          url: "/admin/nextjs-generator",
          icon: Globe,
          description: "Create Next.js layouts and pages"
        },
        {
          title: "Component Library",
          url: "/admin/ai-builders?tab=component-library",
          icon: Layers,
          description: "Browse component templates"
        },
        {
          title: "System Builders",
          url: "/admin/system/builders/demo",
          icon: Code,
          description: "Unified builder system demo"
        },
      ],
    },
  ] as NavigationItem[],
  tools: [
    {
      title: "Workflows",
      url: "/admin/workflows",
      icon: Workflow,
      badge: "New",
      badgeVariant: "default" as const,
      description: "Automated business processes",
      items: [
        {
          title: "All Workflows",
          url: "/admin/workflows",
          icon: Workflow,
          description: "View and manage workflows"
        },
        {
          title: "Workflow Builder",
          url: "/admin/workflows/builder",
          icon: Plus,
          description: "Build custom workflows"
        },
      ],
    },
    {
      title: "System Tools",
      url: "/admin/tools",
      icon: Zap,
      description: "System maintenance and optimization",
      items: [
        {
          title: "Initialize Posts",
          url: "/admin/tools/init-posts",
          icon: Plus,
          description: "Set up initial content"
        },
      ],
    },
    {
      title: "Database",
      url: "/admin/database",
      icon: Database,
      description: "Database management and tools",
    },
    {
      title: "Content Type Builder",
      url: "/admin/content-type-builder",
      icon: Layers,
      badge: "Pro",
      badgeVariant: "default" as const,
      description: "Build custom content structures",
    },
  ] as NavigationItem[],
  settings: [
    {
      title: "Settings",
      url: "/admin/settings",
      icon: Settings,
      description: "System configuration and preferences",
      items: [
        {
          title: "General",
          url: "/admin/settings/general",
          icon: Settings,
          description: "Basic site configuration"
        },
        {
          title: "Store",
          url: "/admin/settings/store",
          icon: Store,
          description: "E-commerce store settings"
        },
        {
          title: "Content",
          url: "/admin/settings/content",
          icon: FileText,
          description: "Content management settings"
        },
        {
          title: "Payments",
          url: "/admin/settings/payments",
          icon: CreditCard,
          description: "Payment gateway configuration"
        },
        {
          title: "Shipping",
          url: "/admin/settings/shipping",
          icon: Truck,
          description: "Shipping methods and zones"
        },
        {
          title: "Taxes",
          url: "/admin/settings/taxes",
          icon: Receipt,
          description: "Tax rates and calculations"
        },
        {
          title: "Security",
          url: "/admin/settings/security",
          icon: Shield,
          description: "Security and authentication"
        },
        {
          title: "Notifications",
          url: "/admin/settings/notifications",
          icon: Bell,
          description: "Notification preferences"
        },
        {
          title: "Integrations",
          url: "/admin/settings/integrations",
          icon: Wifi,
          description: "Third-party integrations"
        },
      ],
    },
    {
      title: "Site Settings",
      url: "/admin/site-settings",
      icon: Globe,
      description: "Global site configuration",
    },
    {
      title: "User Roles",
      url: "/admin/roles",
      icon: Shield,
      description: "Manage user permissions and roles",
    },
  ] as NavigationItem[],
}

export function AdminSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const pathname = usePathname()
  const orderCounts = useOrderCounts()
  // Simplified - no complex state management needed
  const [openItems, setOpenItems] = React.useState<Record<string, boolean>>({
    // Default open states for main sections
    'analytics': false,
    'products': false,
    'orders': false,
    'fulfillments': false,
    'inventory': false,
    'customers': false,
    'page-builder': false,
    'layout-builder': false,
    'themes': false,
    'blog': false,
    'post-types': false,
    'taxonomies': false,
    'pages': false,
    'media': false,
    'reports': false,
    'ai-builders': false,
    'ai-visual-editor': false,
    'nextjs-generator': false,
    'workflows': false,
    'tools': false,
    'database': false,
    'content-type-builder': false,
    'settings': false,
    'site-settings': false,
    'roles': false,
  })

  const toggleItem = (itemKey: string) => {
    setOpenItems(prev => ({
      ...prev,
      [itemKey]: !prev[itemKey]
    }))
  }

  // Auto-open parent menu if child is active
  React.useEffect(() => {
    const newOpenItems = { ...openItems }

    // Check each navigation section for active items
    Object.values(navigationData).flat().forEach(item => {
      if (item.items) {
        const hasActiveChild = item.items.some(subItem => pathname === subItem.url)
        if (hasActiveChild || pathname.startsWith(item.url)) {
          const itemKey = item.url.split('/').pop() || item.title.toLowerCase().replace(/\s+/g, '-')
          newOpenItems[itemKey] = true
        }
      }
    })

    setOpenItems(newOpenItems)
  }, [pathname])

  // Helper function to render navigation items with collapsible support
  const renderNavigationItem = (item: NavigationItem) => {
    const itemKey = item.url.split('/').pop() || item.title.toLowerCase().replace(/\s+/g, '-')
    const isOpen = openItems[itemKey]
    const isActive = pathname === item.url
    const hasActiveChild = item.items?.some(subItem => pathname === subItem.url)

    if (item.items && item.items.length > 0) {
      return (
        <Collapsible
          key={item.title}
          open={isOpen}
          onOpenChange={() => toggleItem(itemKey)}
        >
          <SidebarMenuItem>
            <CollapsibleTrigger asChild>
              <SidebarMenuButton
                tooltip={item.title}
                className={`w-full justify-between ${
                  isActive || hasActiveChild ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''
                }`}
              >
                <div className="flex items-center gap-2">
                  <item.icon className="h-4 w-4" />
                  <span className="truncate">{item.title}</span>
                </div>
                <div className="flex items-center gap-1">
                  {item.badge && (
                    <Badge
                      variant={item.badgeVariant || "secondary"}
                      className="text-xs px-1.5 py-0.5"
                    >
                      {item.badge}
                    </Badge>
                  )}
                  <ChevronRight
                    className={`h-3 w-3 transition-transform duration-200 ${
                      isOpen ? 'rotate-90' : ''
                    }`}
                  />
                </div>
              </SidebarMenuButton>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <SidebarMenuSub>
                {item.items.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton
                      asChild
                      className={pathname === subItem.url ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''}
                    >
                      <Link href={subItem.url} className="flex items-center gap-2">
                        {subItem.icon && <subItem.icon className="h-3 w-3" />}
                        <span className="truncate">{subItem.title}</span>
                        {subItem.badge && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {subItem.badge}
                          </Badge>
                        )}
                      </Link>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            </CollapsibleContent>
          </SidebarMenuItem>
        </Collapsible>
      )
    }

    return (
      <SidebarMenuItem key={item.title}>
        <SidebarMenuButton
          asChild
          tooltip={item.title}
          className={isActive ? 'bg-sidebar-accent text-sidebar-accent-foreground' : ''}
        >
          <Link href={item.url} className="flex items-center gap-2">
            <item.icon className="h-4 w-4" />
            <span className="truncate">{item.title}</span>
            {item.badge && (
              <Badge
                variant={item.badgeVariant || "secondary"}
                className="text-xs px-1.5 py-0.5 ml-auto"
              >
                {item.badge}
              </Badge>
            )}
          </Link>
        </SidebarMenuButton>
      </SidebarMenuItem>
    )
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg" asChild>
              <Link href="/admin">
                <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-pink-600 text-white">
                  <Store className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">Coco Milk Admin</span>
                  <span className="truncate text-xs">Kids Clothing Store</span>
                </div>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <SidebarMenuButton size="sm" className="w-full justify-start text-muted-foreground">
              <Search className="h-4 w-4" />
              <span className="truncate">Search admin...</span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Dashboard</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.main.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Store Management</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.ecommerce.map((item) => renderNavigationItem(item))}
              {/* Dynamic Orders Navigation */}
              {renderNavigationItem(getOrdersNavigation(orderCounts))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Analytics & Reports</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.reports.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Design & Content</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.content.slice(0, 2).map((item) => renderNavigationItem(item))}
              {navigationData.content.slice(2).map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Media</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.content.slice(2).map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>AI Builders</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.aiBuilders.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Tools & Automation</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.tools.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        <SidebarGroup>
          <SidebarGroupLabel>Settings & Configuration</SidebarGroupLabel>
          <SidebarGroupContent>
            <SidebarMenu>
              {navigationData.settings.map((item) => renderNavigationItem(item))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton
                  size="lg"
                  className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
                >
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src="/avatars/admin.png" alt="Admin" />
                    <AvatarFallback className="rounded-lg">AD</AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-semibold">Admin User</span>
                    <span className="truncate text-xs"><EMAIL></span>
                  </div>
                  <ChevronsUpDown className="ml-auto size-4" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-lg"
                side="bottom"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuLabel className="p-0 font-normal">
                  <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                    <Avatar className="h-8 w-8 rounded-lg">
                      <AvatarImage src="/avatars/admin.png" alt="Admin" />
                      <AvatarFallback className="rounded-lg">AD</AvatarFallback>
                    </Avatar>
                    <div className="grid flex-1 text-left text-sm leading-tight">
                      <span className="truncate font-semibold">Admin User</span>
                      <span className="truncate text-xs"><EMAIL></span>
                    </div>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Bell className="mr-2 h-4 w-4" />
                  Notifications
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Shield className="mr-2 h-4 w-4" />
                  Security
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <HelpCircle className="mr-2 h-4 w-4" />
                  Help & Support
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <LogOut className="mr-2 h-4 w-4" />
                  Sign out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
