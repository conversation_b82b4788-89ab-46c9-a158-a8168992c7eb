'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON>roller, Control, useWatch } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Card, CardContent } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Loader2, PackagePlus, X, Plus, Trash2 } from 'lucide-react'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog'
import Image from 'next/image'
import { useDebounce } from '@/hooks/use-debounce'
import { formatCurrency } from '@/lib/utils'

interface Product {
  id: string
  title: string
  slug: string
  price: number
  compareAtPrice?: number
  image?: string
  variants?: ProductVariant[]
}

interface ProductVariant {
  id: string
  title: string
  price: number
  compareAtPrice?: number
  options: Record<string, string>
  image?: string
  sku?: string
}

interface OrderItemFormProps {
  index: number
  control: Control<any>
  remove: () => void
}

export function OrderItemForm({ index, control, remove }: OrderItemFormProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [products, setProducts] = useState<Product[]>([])
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false)
  const debouncedSearch = useDebounce(searchQuery, 300)
  
  const itemData = useWatch({
    control,
    name: `items.${index}`,
  })

  const { field: productIdField } = useController({
    name: `items.${index}.productId`,
    control,
  })

  const { field: variantIdField } = useController({
    name: `items.${index}.variantId`,
    control,
  })

  const { field: quantityField } = useController({
    name: `items.${index}.quantity`,
    control,
  })

  const { field: unitPriceField } = useController({
    name: `items.${index}.unitPrice`,
    control,
  })

  // Selected product and variant data
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null)

  // Fetch products when search query changes
  useEffect(() => {
    if (!debouncedSearch && !isProductSelectorOpen) return
    
    const fetchProducts = async () => {
      setIsLoading(true)
      try {
        const params = new URLSearchParams()
        if (debouncedSearch) params.append('query', debouncedSearch)
        
        const res = await fetch(`/api/e-commerce/products?${params}`)
        if (res.ok) {
          const data = await res.json()
          setProducts(data.data || [])
        }
      } catch (error) {
        console.error('Failed to fetch products:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchProducts()
  }, [debouncedSearch, isProductSelectorOpen])

  // Fetch product details when productId changes
  useEffect(() => {
    if (!productIdField.value) {
      setSelectedProduct(null)
      setSelectedVariant(null)
      return
    }
    
    const fetchProductDetails = async () => {
      try {
        const res = await fetch(`/api/e-commerce/products/${productIdField.value}`)
        if (res.ok) {
          const data = await res.json()
          setSelectedProduct(data.data)
          
          // Set selected variant if variantId exists
          if (variantIdField.value && data.data.variants) {
            const variant = data.data.variants.find(
              (v: ProductVariant) => v.id === variantIdField.value
            )
            setSelectedVariant(variant || null)
          } else if (data.data.variants && data.data.variants.length > 0) {
            // Default to first variant if none selected
            setSelectedVariant(data.data.variants[0])
            variantIdField.onChange(data.data.variants[0].id)
            unitPriceField.onChange(data.data.variants[0].price)
          } else {
            // No variants, use main product price
            setSelectedVariant(null)
            variantIdField.onChange('')
            unitPriceField.onChange(data.data.price)
          }
        }
      } catch (error) {
        console.error('Failed to fetch product details:', error)
      }
    }

    fetchProductDetails()
  }, [productIdField.value])

  // Update price when variant changes
  useEffect(() => {
    if (!selectedProduct) return
    
    if (variantIdField.value && selectedProduct.variants) {
      const variant = selectedProduct.variants.find(v => v.id === variantIdField.value)
      if (variant) {
        unitPriceField.onChange(variant.price)
        setSelectedVariant(variant)
      }
    } else if (!variantIdField.value) {
      unitPriceField.onChange(selectedProduct.price)
      setSelectedVariant(null)
    }
  }, [variantIdField.value, selectedProduct])

  const handleSelectProduct = (product: Product) => {
    productIdField.onChange(product.id)
    setIsProductSelectorOpen(false)
  }

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value)
    quantityField.onChange(isNaN(value) || value < 1 ? 1 : value)
  }

  const totalPrice = (unitPriceField.value || 0) * (quantityField.value || 1)

  return (
    <Card className="mb-4">
      <CardContent className="pt-4">
        <div className="flex flex-col space-y-4">
          {/* Product selection */}
          <div className="grid grid-cols-1 md:grid-cols-[1fr_auto] gap-3">
            <div>
              <Label htmlFor={`product-${index}`}>Product</Label>
              <div className="flex gap-2 items-center mt-1">
                {selectedProduct ? (
                  <div className="flex flex-1 items-center gap-3 border rounded-md p-2">
                    {selectedProduct.image && (
                      <div className="h-10 w-10 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                        <Image 
                          src={selectedProduct.image} 
                          alt={selectedProduct.title}
                          width={40}
                          height={40}
                          className="object-cover"
                        />
                      </div>
                    )}
                    <div className="flex-1 min-w-0">
                      <div className="text-sm font-medium truncate">{selectedProduct.title}</div>
                      {selectedVariant && selectedVariant.title !== 'Default' && (
                        <div className="text-xs text-muted-foreground truncate">
                          Variant: {selectedVariant.title}
                        </div>
                      )}
                    </div>
                    <Button 
                      type="button" 
                      size="icon" 
                      variant="ghost"
                      onClick={() => setIsProductSelectorOpen(true)}
                    >
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                ) : (
                  <Dialog open={isProductSelectorOpen} onOpenChange={setIsProductSelectorOpen}>
                    <DialogTrigger asChild>
                      <Button 
                        variant="outline" 
                        className="w-full justify-start"
                      >
                        <PackagePlus className="h-4 w-4 mr-2" />
                        Select product
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Select Product</DialogTitle>
                      </DialogHeader>
                      <div className="space-y-4">
                        <Input
                          placeholder="Search products..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                        />
                        
                        {isLoading ? (
                          <div className="py-8 flex justify-center">
                            <Loader2 className="h-8 w-8 animate-spin" />
                          </div>
                        ) : products.length === 0 ? (
                          <div className="py-8 text-center">
                            <p className="text-muted-foreground">No products found</p>
                          </div>
                        ) : (
                          <div className="max-h-[400px] overflow-y-auto space-y-2">
                            {products.map((product) => (
                              <div
                                key={product.id}
                                className="flex items-center p-2 border rounded-md hover:bg-accent cursor-pointer"
                                onClick={() => handleSelectProduct(product)}
                              >
                                {product.image ? (
                                  <div className="h-12 w-12 bg-gray-100 rounded overflow-hidden flex-shrink-0">
                                    <Image 
                                      src={product.image} 
                                      alt={product.title}
                                      width={48}
                                      height={48}
                                      className="object-cover"
                                    />
                                  </div>
                                ) : (
                                  <div className="h-12 w-12 bg-gray-100 rounded flex items-center justify-center flex-shrink-0">
                                    <PackagePlus className="h-6 w-6 text-gray-400" />
                                  </div>
                                )}
                                <div className="ml-3 flex-1">
                                  <div className="font-medium">{product.title}</div>
                                  <div className="text-sm text-muted-foreground">
                                    {formatCurrency(product.price)} • SKU: {product.id.slice(-6).toUpperCase()}
                                  </div>
                                </div>
                                {product.variants && product.variants.length > 0 && (
                                  <span className="text-xs text-muted-foreground">
                                    {product.variants.length} variant(s)
                                  </span>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </DialogContent>
                  </Dialog>
                )}
              </div>
            </div>
            <div>
              <Button 
                type="button" 
                size="icon" 
                variant="outline" 
                className="mt-7" 
                onClick={remove}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
          
          {/* Variant selection */}
          {selectedProduct && selectedProduct.variants && selectedProduct.variants.length > 0 && (
            <div>
              <Label htmlFor={`variant-${index}`}>Variant</Label>
              <Select
                value={variantIdField.value || ''}
                onValueChange={variantIdField.onChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select variant" />
                </SelectTrigger>
                <SelectContent>
                  {selectedProduct.variants.map((variant) => (
                    <SelectItem key={variant.id} value={variant.id}>
                      {variant.title} - {formatCurrency(variant.price)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
          
          {/* Quantity and price */}
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor={`quantity-${index}`}>Quantity</Label>
              <Input
                id={`quantity-${index}`}
                type="number"
                min="1"
                value={quantityField.value || 1}
                onChange={handleQuantityChange}
              />
            </div>
            <div>
              <Label htmlFor={`price-${index}`}>Unit Price</Label>
              <div className="relative">
                <span className="absolute left-3 top-2.5">$</span>
                <Input
                  id={`price-${index}`}
                  type="number"
                  step="0.01"
                  min="0"
                  className="pl-7"
                  value={unitPriceField.value || 0}
                  onChange={e => unitPriceField.onChange(parseFloat(e.target.value) || 0)}
                />
              </div>
            </div>
          </div>
          
          {/* Total */}
          <div className="text-right font-medium">
            Total: {formatCurrency(totalPrice)}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 